<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Book Your Massage Appointment</title>

    <!-- External CSS and Font Resources -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Lato:300,400|Poppins:300,400,800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@200..800&display=swap" rel="stylesheet">

    <!-- Custom Styles -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Lato', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .booking-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 1200px;
            min-height: 80vh;
            position: relative;
        }

        .booking-header {
            background: linear-gradient(135deg, hsl(184, 70%, 35%) 0%, hsl(184, 70%, 45%) 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .booking-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .booking-header h1 {
            font-family: 'Poppins', sans-serif;
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 1;
        }

        .booking-header p {
            font-size: 1.1rem;
            opacity: 0.9;
            letter-spacing: 1px;
            position: relative;
            z-index: 1;
        }

        .back-button {
            position: absolute;
            top: 2rem;
            left: 2rem;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 0.75rem 1rem;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Lato', sans-serif;
            font-weight: 500;
            z-index: 2;
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .iframe-container {
            position: relative;
            padding-bottom: 1200px;
            height: 0;
            overflow: hidden;
            background: #f8f9fa;
        }

        .iframe-container iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            z-index: 1;
            transition: opacity 0.5s ease;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #e3e3e3;
            border-top: 4px solid hsl(184, 70%, 40%);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }

        .loading-text {
            color: #666;
            font-size: 1.1rem;
            font-weight: 500;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .booking-container {
                min-height: 90vh;
                border-radius: 15px;
            }

            .booking-header {
                padding: 1.5rem;
            }

            .booking-header h1 {
                font-size: 2rem;
            }

            .back-button {
                top: 1.5rem;
                left: 1.5rem;
                padding: 0.5rem 0.75rem;
            }

            .iframe-container {
                padding-bottom: 1400px;
            }
        }
    </style>
</head>
<body>
    <div class="booking-container">
        <!-- Header -->
        <header class="booking-header">
            <button class="back-button" onclick="goBack()" aria-label="Go back to booking form">
                <i class="fas fa-arrow-left mr-2"></i> Back
            </button>
            <h1>Book Your Appointment</h1>
            <p>Complete your booking with our scheduling system</p>
        </header>

        <!-- Setmore Iframe Container -->
        <div class="iframe-container">
            <!-- Loading overlay -->
            <div class="loading-overlay" id="loading-overlay">
                <div class="loading-spinner"></div>
                <div class="loading-text">Loading booking system...</div>
            </div>
            
            <!-- Setmore iframe -->
            <iframe
                src="https://YOUR-SETMORE-URL.setmore.com"
                loading="lazy"
                sandbox="allow-scripts allow-forms allow-same-origin"
                referrerpolicy="no-referrer"
                title="Book an Appointment"
                onload="hideLoading()"
            ></iframe>
        </div>
    </div>

    <script>
        function goBack() {
            // Go back to the previous page (booking form)
            window.history.back();
        }

        function hideLoading() {
            // Hide the loading overlay when iframe loads
            const loadingOverlay = document.getElementById('loading-overlay');
            if (loadingOverlay) {
                loadingOverlay.style.opacity = '0';
                setTimeout(() => {
                    loadingOverlay.style.display = 'none';
                }, 500);
            }
        }

        // Handle cases where iframe might not trigger onload
        setTimeout(() => {
            hideLoading();
        }, 5000); // Hide loading after 5 seconds regardless
    </script>
</body>
</html>
