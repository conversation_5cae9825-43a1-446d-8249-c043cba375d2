
/**
 * Massage Booking Application
 * A modular application for booking massage services
 */
const MassageBooking = {
    /**
     * Data Module - Contains all application data
     */
    Data: {
        /**
         * Person data - Number of people options
         * @type {Array<Object>}
         */
        persons: [
            {
                id: 'individual',
                name: 'Individual',
                description: 'One person massage session',
                icon: 'fa-solid fa-user',
                count: 1
            },
            {
                id: 'couples',
                name: '<PERSON><PERSON><PERSON>',
                description: 'Back-to-back individual sessions for two',
                icon: 'fa-solid fa-user-group',
                count: 2
            },
            {
                id: 'corporate',
                name: 'Corporate',
                description: 'Group sessions for four or more people',
                icon: 'fa-solid fa-users',
                count: 3
            }
        ],

        /**
         * Service data - Massage styles with their properties
         * @type {Array<Object>}
         */
        services: [
            {
                id: 'swedish',
                name: 'Swedish Massage',
                description: 'Classic, calming, and great for overall relaxation',
                icon: 'fa-solid fa-spa',
                relaxationScore: 9
            },
            {
                id: 'deepTissue',
                name: 'Deep Tissue Massage',
                description: 'Targets deep muscle layers to release tension',
                icon: 'fa-solid fa-dumbbell',
                relaxationScore: 3
            },
            {
                id: 'sports',
                name: 'Sports Massage',
                description: 'Athletic recovery and performance enhancement',
                icon: 'fa-solid fa-person-running',
                relaxationScore: 4
            },
            {
                id: 'neuromuscular',
                name: 'Neuromuscular Therapy',
                description: 'Focused work on trigger points to help relieve pain',
                icon: 'fa-solid fa-brain',
                relaxationScore: 5
            },
            {
                id: 'chair',
                name: 'Chair Massage',
                description: 'Quick relief for neck, back, and shoulders—fully clothed.',
                icon: 'fa-solid fa-chair',
                relaxationScore: 2,
                hasCustomDurations: true
            }
        ],

        /**
         * Duration data - Time options with prices and relaxation scores
         */
        durations: {
            /**
             * Standard durations for most massage types
             * @type {Array<Object>}
             */
            standard: [
                { time: 60,  price: 100, relaxationScore: 3 },
                { time: 90,  price: 135, relaxationScore: 6 },
                { time: 120, price: 200, relaxationScore: 9 }
            ],

            /**
             * Durations specific to couples massage
             * @type {Array<Object>}
             */
            couples: [
                { time: 60, price: 170, relaxationScore: 4 },
                { time: 90, price: 240, relaxationScore: 7 }
            ],

            /**
             * Durations specific to chair massage
             * @type {Array<Object>}
             */
            chair: [
                { time: 60,  price: 125, relaxationScore: 1 },
                { time: 90,  price: 150, relaxationScore: 2 },
                { time: 120, price: 225, relaxationScore: 4 }
            ]
        },

        /**
         * Corporate session configuration
         * @type {Object}
         */
        corporate: {
            sessionDuration: 15, // minutes per session
            sessionPrice: 25,    // price per session
            minSessions: 4,      // minimum sessions required
            maxSessions: 40,     // maximum sessions (10 hours)
            relaxationScore: 2   // base relaxation score for corporate sessions
        },

        /**
         * Get durations for a specific service type and person type
         * @param {string} serviceId - The ID of the service
         * @param {string} personId - The ID of the person type
         * @returns {Array<Object>} Array of duration options
         */
        getDurationsForService: function(serviceId, personId) {
            if (personId === 'couples') {
                return this.durations.couples;
            } else if (serviceId === 'chair') {
                return this.durations.chair;
            } else {
                return this.durations.standard;
            }
        }
    },

    /**
     * State Module - Manages application state
     */
    State: {
        /**
         * Current selected person type
         * @type {Object|null}
         */
        selectedPerson: null,

        /**
         * Current selected service
         * @type {Object|null}
         */
        selectedService: null,

        /**
         * Current selected duration
         * @type {Object|null}
         */
        selectedDuration: null,

        /**
         * Current group size for corporate sessions
         * @type {number}
         */
        groupSize: 3,

        /**
         * Maximum possible relaxation score
         * @type {number}
         */
        maxRelaxationScore: 1,

        /**
         * Whether the person section is collapsed
         * @type {boolean}
         */
        personCollapsed: false,

        /**
         * Whether the service section is collapsed
         * @type {boolean}
         */
        serviceCollapsed: false,

        /**
         * Whether the duration section is collapsed
         * @type {boolean}
         */
        durationCollapsed: false,

        /**
         * Whether the group size section is collapsed
         * @type {boolean}
         */
        groupSizeCollapsed: false,

        /**
         * Whether the unified selection section is collapsed
         * @type {boolean}
         */
        unifiedSelectionCollapsed: false,

        /**
         * Whether the corporate sessions section is collapsed
         * @type {boolean}
         */
        corporateSessionsCollapsed: false,

        /**
         * Whether the summary animation has been played
         * @type {boolean}
         */
        summaryAnimationPlayed: false,

        /**
         * Selected duration and group size for corporate sessions (legacy)
         * @type {Object|null}
         */
        selectedUnifiedOption: null,

        /**
         * Number of corporate sessions selected
         * @type {number}
         */
        corporateSessions: 4,

        /**
         * Reset the state to initial values
         */
        reset: function() {
            this.selectedPerson = null;
            this.selectedService = null;
            this.selectedDuration = null;
            this.groupSize = 3;
            this.selectedUnifiedOption = null;
            this.corporateSessions = 4;
            this.maxRelaxationScore = 1;
            this.personCollapsed = false;
            this.groupSizeCollapsed = false;
            this.unifiedSelectionCollapsed = false;
            this.corporateSessionsCollapsed = false;
            this.serviceCollapsed = false;
            this.durationCollapsed = false;
            this.summaryAnimationPlayed = false;
        }
    },

    /**
     * DOM Module - Manages DOM references and manipulations
     */
    DOM: {
        /**
         * References to DOM elements
         */
        elements: {
            // Main containers
            personOptionsContainer: null,
            serviceOptionsContainer: null,
            durationOptionsContainer: null,
            bookNowContainer: null,

            // Section containers
            personSection: null,
            groupSizeSection: null,
            unifiedSelectionSection: null,
            serviceSection: null,
            durationSection: null,
            personSummary: null,
            groupSizeSummary: null,
            unifiedSelectionSummary: null,
            serviceSummary: null,
            durationSummary: null,

            // Interactive elements
            relaxationLevelText: null,
            bookButton: null,
            editPersonButton: null,
            editGroupSizeButton: null,
            editUnifiedSelectionButton: null,
            editServiceButton: null,
            editDurationButton: null,
            personNextButton: null,
            groupSizeNextButton: null,
            serviceNextButton: null,
            unifiedSelectionNextButton: null,

            // Group size counter elements
            peopleCountDisplay: null,
            decreasePeopleButton: null,
            increasePeopleButton: null,

            // Unified selection elements (legacy)
            unifiedSelectionInterface: null,
            durationGroupCards: null,

            // Corporate session elements
            corporateSessionsCounter: null,
            corporateSessionsDisplay: null,
            corporateDecreaseSessions: null,
            corporateIncreaseSessions: null,
            corporateSessionsInfo: null,
            corporateTotalPrice: null,
            corporateTotalTime: null
        },

        /**
         * Cache all DOM element references
         */
        cacheElements: function() {
            // Main containers
            this.elements.personOptionsContainer = document.getElementById('person-options');
            this.elements.serviceOptionsContainer = document.getElementById('service-options');
            this.elements.durationOptionsContainer = document.getElementById('duration-options');
            this.elements.bookNowContainer = document.getElementById('book-now-container');

            // Section containers
            this.elements.personSection = document.getElementById('person-section');
            this.elements.groupSizeSection = document.getElementById('group-size-section');
            this.elements.unifiedSelectionSection = document.getElementById('unified-selection-section');
            this.elements.serviceSection = document.getElementById('service-section');
            this.elements.durationSection = document.getElementById('duration-section');
            this.elements.personSummary = document.getElementById('person-summary');
            this.elements.groupSizeSummary = document.getElementById('group-size-summary');
            this.elements.unifiedSelectionSummary = document.getElementById('unified-selection-summary');
            this.elements.serviceSummary = document.getElementById('service-summary');
            this.elements.durationSummary = document.getElementById('duration-summary');

            // Interactive elements
            this.elements.relaxationLevelText = document.getElementById('relaxation-level-text');
            this.elements.bookButton = document.getElementById('book-button');
            this.elements.editPersonButton = document.getElementById('edit-person-button');
            this.elements.editGroupSizeButton = document.getElementById('edit-group-size-button');
            this.elements.editUnifiedSelectionButton = document.getElementById('edit-unified-selection-button');
            this.elements.editServiceButton = document.getElementById('edit-service-button');
            this.elements.editDurationButton = document.getElementById('edit-duration-button');
            this.elements.personNextButton = document.getElementById('person-next-button');
            this.elements.groupSizeNextButton = document.getElementById('group-size-next-button');
            this.elements.serviceNextButton = document.getElementById('service-next-button');
            this.elements.unifiedSelectionNextButton = document.getElementById('unified-selection-next-button');

            // Group size counter elements
            this.elements.peopleCountDisplay = document.getElementById('people-count');
            this.elements.decreasePeopleButton = document.getElementById('decrease-people');
            this.elements.increasePeopleButton = document.getElementById('increase-people');

            // Unified selection elements (legacy)
            this.elements.unifiedSelectionInterface = document.getElementById('unified-selection-interface');
            this.elements.durationGroupCards = document.getElementById('duration-group-cards');

            // Corporate session elements
            this.elements.corporateSessionsCounter = document.getElementById('corporate-sessions-counter');
            this.elements.corporateSessionsDisplay = document.getElementById('corporate-sessions-display');
            this.elements.corporateDecreaseSessions = document.getElementById('corporate-decrease-sessions');
            this.elements.corporateIncreaseSessions = document.getElementById('corporate-increase-sessions');
            this.elements.corporateSessionsInfo = document.getElementById('corporate-sessions-info');
            this.elements.corporateTotalPrice = document.getElementById('corporate-total-price');
            this.elements.corporateTotalTime = document.getElementById('corporate-total-time');
        },

        /**
         * Toggle element visibility
         * @param {HTMLElement} element - The element to toggle
         * @param {boolean} show - Whether to show or hide the element
         */
        toggleVisibility: function(element, show) {
            if (!element) return; // Guard against null elements

            if (show) {
                element.classList.remove('hidden');
            } else {
                element.classList.add('hidden');
            }
        },

        /**
         * Toggle a class on an element
         * @param {HTMLElement} element - The element to modify
         * @param {string} className - The class to toggle
         * @param {boolean} add - Whether to add or remove the class
         */
        toggleClass: function(element, className, add) {
            if (add) {
                element.classList.add(className);
            } else {
                element.classList.remove(className);
            }
        }
    },

    /**
     * Calculator Module - Handles price and relaxation calculations
     */
    Calculator: {
        /**
         * Calculate price for a duration based on selected service and group size
         * @param {Object} duration - The duration object
         * @returns {number} The calculated price
         */
        calculatePriceForDuration: function(duration) {
            const state = MassageBooking.State;

            if (!state.selectedService) return duration.price;

            let price = duration.price;
            if (state.selectedService.priceAdjustment &&
                state.selectedService.id !== 'couples' &&
                state.selectedService.id !== 'chair') {
                price += state.selectedService.priceAdjustment;
            }

            // For corporate sessions, multiply by group size
            if (state.selectedPerson && state.selectedPerson.id === 'corporate') {
                price *= state.groupSize;
            }

            return price;
        },

        /**
         * Calculate total price for the selected service and duration
         * @returns {number} The total price
         */
        calculateTotalPrice: function() {
            const state = MassageBooking.State;

            // For corporate sessions, use session-based pricing
            if (state.selectedPerson && state.selectedPerson.id === 'corporate') {
                return this.calculateCorporatePrice();
            }

            if (!state.selectedService || !state.selectedDuration) return 0;

            return this.calculatePriceForDuration(state.selectedDuration);
        },

        /**
         * Calculate total price for corporate sessions
         * @returns {number} The total price for corporate sessions
         */
        calculateCorporatePrice: function() {
            const state = MassageBooking.State;
            const corporateConfig = MassageBooking.Data.corporate;

            return state.corporateSessions * corporateConfig.sessionPrice;
        },

        /**
         * Calculate total time for corporate sessions in minutes
         * @returns {number} Total time in minutes
         */
        calculateCorporateTime: function() {
            const state = MassageBooking.State;
            const corporateConfig = MassageBooking.Data.corporate;

            return state.corporateSessions * corporateConfig.sessionDuration;
        },

        /**
         * Format corporate time as hours and minutes
         * @returns {string} Formatted time string
         */
        formatCorporateTime: function() {
            const totalMinutes = this.calculateCorporateTime();
            const hours = Math.floor(totalMinutes / 60);
            const minutes = totalMinutes % 60;

            if (hours === 0) {
                return `${minutes} minutes`;
            } else if (minutes === 0) {
                return `${hours} hour${hours > 1 ? 's' : ''}`;
            } else {
                return `${hours} hour${hours > 1 ? 's' : ''} ${minutes} minutes`;
            }
        },

        /**
         * Calculate relaxation score based on selected service and duration
         * @returns {number} The relaxation score
         */
        calculateRelaxationScore: function() {
            const state = MassageBooking.State;

            if (!state.selectedService) return 0;

            let score = state.selectedService.relaxationScore;

            if (state.selectedDuration) {
                score += state.selectedDuration.relaxationScore;
            }

            return score;
        },

        /**
         * Calculate maximum possible relaxation score for the selected service
         */
        recalculateMaxRelaxationScore: function() {
            const state = MassageBooking.State;

            if (!state.selectedService || !state.selectedPerson) {
                state.maxRelaxationScore = 1;
                return;
            }

            // Get appropriate durations for the selected service and person type
            const durations = MassageBooking.Data.getDurationsForService(state.selectedService.id, state.selectedPerson.id);

            // Find the maximum relaxation score from available durations
            const maxDurationScore = Math.max(...durations.map(d => d.relaxationScore));

            // Update the maximum relaxation score
            state.maxRelaxationScore = Math.max(1, state.selectedService.relaxationScore + maxDurationScore);
        }
    },

    /**
     * UI Module - Manages UI state and interactions
     */
    UI: {
        /**
         * Initialize the calculator UI
         */
        init: function() {
            // Populate person options
            this.populatePersonOptions();

            // Populate service options
            this.populateServices();

            // Set up event listeners
            this.setupEventListeners();

            // Update UI state
            this.updateUI();
        },

        /**
         * Populate the corporate sessions interface
         */
        populateCorporateSessions: function() {
            const state = MassageBooking.State;
            const dom = MassageBooking.DOM.elements;

            if (!state.selectedService || !state.selectedPerson || state.selectedPerson.id !== 'corporate') {
                return;
            }

            // Make sure summary is hidden when showing the interface
            MassageBooking.DOM.toggleVisibility(dom.unifiedSelectionSummary, false);

            // Update the session counter display
            this.updateCorporateSessionsDisplay();

            // Set up event listeners for session counter
            this.setupCorporateSessionsListeners();
        },

        /**
         * Update the corporate sessions display
         */
        updateCorporateSessionsDisplay: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;
            const corporateConfig = MassageBooking.Data.corporate;
            const calculator = MassageBooking.Calculator;

            if (!dom.corporateSessionsDisplay) return;

            // Update sessions display
            dom.corporateSessionsDisplay.textContent = state.corporateSessions;

            // Update total price
            if (dom.corporateTotalPrice) {
                const totalPrice = calculator.calculateCorporatePrice();
                dom.corporateTotalPrice.textContent = `$${totalPrice}`;
            }

            // Update total time
            if (dom.corporateTotalTime) {
                const timeString = calculator.formatCorporateTime();
                dom.corporateTotalTime.textContent = timeString;
            }

            // Update button states
            if (dom.corporateDecreaseSessions) {
                dom.corporateDecreaseSessions.disabled = state.corporateSessions <= corporateConfig.minSessions;
            }

            if (dom.corporateIncreaseSessions) {
                dom.corporateIncreaseSessions.disabled = state.corporateSessions >= corporateConfig.maxSessions;
            }

            // Update info text
            if (dom.corporateSessionsInfo) {
                dom.corporateSessionsInfo.innerHTML = `
                    <i class="fas fa-info-circle" style="margin-right: 0.5rem; color: hsl(184, 70%, 35%);"></i>
                    15-minute sessions • $${corporateConfig.sessionPrice} each • Minimum ${corporateConfig.minSessions} sessions required
                `;
            }

            // Show Next button when minimum sessions are selected and section is not collapsed
            if (dom.unifiedSelectionNextButton) {
                MassageBooking.DOM.toggleVisibility(
                    dom.unifiedSelectionNextButton,
                    state.corporateSessions >= corporateConfig.minSessions && !state.corporateSessionsCollapsed
                );
            }
        },

        /**
         * Set up event listeners for corporate sessions counter
         */
        setupCorporateSessionsListeners: function() {
            const dom = MassageBooking.DOM.elements;

            // Check if listeners are already set up to prevent duplicates
            if (dom.corporateDecreaseSessions && !dom.corporateDecreaseSessions.hasAttribute('data-listener-added')) {
                dom.corporateDecreaseSessions.addEventListener('click', () => {
                    this.adjustCorporateSessions(-4); // Decrease by 4 sessions (1 hour)
                });
                dom.corporateDecreaseSessions.setAttribute('data-listener-added', 'true');
            }

            if (dom.corporateIncreaseSessions && !dom.corporateIncreaseSessions.hasAttribute('data-listener-added')) {
                dom.corporateIncreaseSessions.addEventListener('click', () => {
                    this.adjustCorporateSessions(4); // Increase by 4 sessions (1 hour)
                });
                dom.corporateIncreaseSessions.setAttribute('data-listener-added', 'true');
            }
        },

        /**
         * Adjust the number of corporate sessions
         * @param {number} change - Change amount (positive or negative)
         */
        adjustCorporateSessions: function(change) {
            const state = MassageBooking.State;
            const corporateConfig = MassageBooking.Data.corporate;

            const newSessions = state.corporateSessions + change;

            if (newSessions >= corporateConfig.minSessions && newSessions <= corporateConfig.maxSessions) {
                state.corporateSessions = newSessions;
                this.updateCorporateSessionsDisplay();
                this.updateUI(); // Update the overall UI state
            }
        },

        /**
         * Populate the unified selection interface for corporate sessions (LEGACY - kept for compatibility)
         */
        populateUnifiedSelection: function() {
            // For corporate sessions, use the new session-based interface
            if (MassageBooking.State.selectedPerson && MassageBooking.State.selectedPerson.id === 'corporate') {
                this.populateCorporateSessions();
                return;
            }

            // Legacy code for other person types (if needed)
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;

            if (!state.selectedService || !state.selectedPerson || state.selectedPerson.id !== 'corporate') {
                return;
            }

            // Clear existing cards
            dom.durationGroupCards.innerHTML = '';
        },



        /**
         * Select a unified option (duration + group size)
         * @param {number} durationTime - Duration in minutes
         * @param {number} groupSize - Number of people
         */
        selectUnifiedOption: function(durationTime, groupSize) {
            const state = MassageBooking.State;
            const durations = MassageBooking.Data.getDurationsForService(state.selectedService.id, 'individual');
            const selectedDuration = durations.find(d => d.time === durationTime);

            if (!selectedDuration) return;

            // Update state
            state.selectedDuration = selectedDuration;
            state.groupSize = groupSize;
            state.selectedUnifiedOption = {
                duration: selectedDuration,
                groupSize: groupSize,
                totalPrice: selectedDuration.price * groupSize
            };

            // Update visual selection
            document.querySelectorAll('.duration-group-card').forEach(card => {
                card.classList.remove('selected');
            });

            const selectedCard = document.querySelector(`[data-duration="${durationTime}"]`);
            if (selectedCard) {
                selectedCard.classList.add('selected');
            }

            // Update UI
            this.updateUI();

            // Scroll to booking section after a brief delay to allow UI updates
            setTimeout(() => {
                this.scrollToBookingSection();
            }, 300);
        },

        /**
         * Set up event listeners for UI controls
         */
        setupEventListeners: function() {
            const dom = MassageBooking.DOM.elements;

            // Edit person button
            dom.editPersonButton.addEventListener('click', () => {
                this.expandPersonSection();
                this.hideServiceSection();
                this.hideGroupSizeSection();
                this.hideDurationSection();

                // Scroll to person section
                setTimeout(() => {
                    this.scrollToSection(dom.personSection, 200);
                }, 100);
            });

            // Edit group size button (only if it exists)
            if (dom.editGroupSizeButton) {
                dom.editGroupSizeButton.addEventListener('click', () => {
                    this.expandGroupSizeSection();
                    this.hideDurationSection();

                    // Scroll to group size section
                    setTimeout(() => {
                        this.scrollToSection(dom.groupSizeSection, 200);
                    }, 100);
                });
            }

            // Edit unified selection button (corporate sessions)
            const editUnifiedSelectionButton = document.getElementById('edit-unified-selection-button');

            if (editUnifiedSelectionButton) {
                editUnifiedSelectionButton.addEventListener('click', () => {
                    this.expandCorporateSessionsSection();

                    // Scroll to unified selection section
                    setTimeout(() => {
                        this.scrollToSection(dom.unifiedSelectionSection, 200);
                    }, 100);
                });
            }

            // Edit service button
            if (dom.editServiceButton) {
                dom.editServiceButton.addEventListener('click', () => {
                    this.expandServiceSection();
                    this.hideGroupSizeSection();
                    if (MassageBooking.State.durationCollapsed) {
                        this.collapseDurationSection();
                    } else {
                        this.hideDurationSection();
                    }

                    // Scroll to service section
                    setTimeout(() => {
                        this.scrollToSection(dom.serviceSection, 200);
                    }, 100);
                });
            }

            // Edit duration button
            if (dom.editDurationButton) {
                dom.editDurationButton.addEventListener('click', () => {
                    this.expandDurationSection();

                    // Scroll to duration section
                    setTimeout(() => {
                        this.scrollToSection(dom.durationSection, 200);
                    }, 100);
                });
            }

            // Next button for person section
            if (dom.personNextButton) {
                dom.personNextButton.addEventListener('click', () => {
                    this.collapsePersonSection();
                    this.showServiceSection();

                    // Scroll to service section
                    setTimeout(() => {
                        this.scrollToSection(dom.serviceSection, 200);
                    }, 100);
                });
            }

            // Next button for group size section (only if it exists)
            if (dom.groupSizeNextButton) {
                dom.groupSizeNextButton.addEventListener('click', () => {
                    this.collapseGroupSizeSection();
                    this.showDurationSection();

                    // Scroll to duration section
                    setTimeout(() => {
                        this.scrollToSection(dom.durationSection, 200);
                    }, 100);
                });
            }

            // Next button for service section
            if (dom.serviceNextButton) {
                dom.serviceNextButton.addEventListener('click', () => {
                    this.collapseServiceSection();

                    // Show unified selection for corporate, otherwise show duration section
                    if (MassageBooking.State.selectedPerson && MassageBooking.State.selectedPerson.id === 'corporate') {
                        this.showUnifiedSelection();

                        // Scroll to unified selection section
                        setTimeout(() => {
                            this.scrollToSection(dom.unifiedSelectionSection, 200);
                        }, 100);
                    } else {
                        this.showDurationSection();

                        // Scroll to duration section
                        setTimeout(() => {
                            this.scrollToSection(dom.durationSection, 200);
                        }, 100);
                    }
                });
            }

            // Next button for unified selection (corporate sessions)
            if (dom.unifiedSelectionNextButton) {
                dom.unifiedSelectionNextButton.addEventListener('click', () => {
                    this.collapseCorporateSessionsSection();

                    // Scroll to booking section
                    setTimeout(() => {
                        this.scrollToBookingSection();
                    }, 300);
                });
            }

            // Book button
            if (dom.bookButton) {
                dom.bookButton.addEventListener('click', () => {
                    this.handleBooking();
                });
            }

            // Group size counter buttons (only if they exist)
            if (dom.decreasePeopleButton) {
                dom.decreasePeopleButton.addEventListener('click', () => {
                    this.decreaseGroupSize();
                });
            }

            if (dom.increasePeopleButton) {
                dom.increasePeopleButton.addEventListener('click', () => {
                    this.increaseGroupSize();
                });
            }
        },

        /**
         * Collapse the person section and show summary
         */
        collapsePersonSection: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;

            if (state.selectedPerson) {
                // Hide person options and next button
                MassageBooking.DOM.toggleVisibility(dom.personOptionsContainer, false);
                MassageBooking.DOM.toggleVisibility(dom.personNextButton, false);

                // Hide the person header with the "Choose Number of People" text
                MassageBooking.DOM.toggleVisibility(document.getElementById('person-header'), false);

                // Show the person summary in place of the header
                MassageBooking.DOM.toggleVisibility(dom.personSummary, true);
                document.getElementById('person-summary-text').textContent = state.selectedPerson.name;

                // Update state
                state.personCollapsed = true;

                // Force the service section to be visible
                const serviceSection = document.getElementById('service-section');
                serviceSection.classList.remove('hidden-section');
                serviceSection.style.display = 'block';
                serviceSection.classList.remove('hidden');

                // Reset animation for service options
                document.querySelectorAll('#service-options .service-option').forEach((option, index) => {
                    // Reset the animation by removing and re-adding the element to the DOM
                    option.style.animation = 'none';
                    option.offsetHeight; // Trigger reflow
                    option.style.animation = '';
                    option.style.setProperty('--animation-order', index);
                });
            }
        },

        /**
         * Expand the person section to show all options
         */
        expandPersonSection: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;

            // Show person options and hide summary
            MassageBooking.DOM.toggleVisibility(dom.personOptionsContainer, true);
            MassageBooking.DOM.toggleVisibility(dom.personSummary, false);

            // Show the person header with the "Choose Number of People" text
            MassageBooking.DOM.toggleVisibility(document.getElementById('person-header'), true);

            // Show next button if a person is selected
            if (state.selectedPerson) {
                MassageBooking.DOM.toggleVisibility(dom.personNextButton, true);
            }

            // Update state
            state.personCollapsed = false;

            // Reset group size state when editing person selection
            state.groupSizeCollapsed = false;

            // Repopulate service options based on the current person selection
            this.populateServices();

            // Reset animation for person options
            document.querySelectorAll('#person-options .service-option').forEach((option, index) => {
                // Reset the animation by removing and re-adding the element to the DOM
                option.style.animation = 'none';
                option.offsetHeight; // Trigger reflow
                option.style.animation = '';
                option.style.setProperty('--animation-order', index);
            });
        },

        /**
         * Show the group size section
         */
        showGroupSizeSection: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;

            MassageBooking.DOM.toggleVisibility(dom.groupSizeSection, true);

            // If group size is already collapsed, keep it collapsed
            if (state.groupSizeCollapsed) {
                // Make sure the summary is shown and counter is hidden
                MassageBooking.DOM.toggleVisibility(document.getElementById('group-size-counter'), false);
                MassageBooking.DOM.toggleVisibility(dom.groupSizeNextButton, false);
                MassageBooking.DOM.toggleVisibility(document.getElementById('group-size-header'), false);
                MassageBooking.DOM.toggleVisibility(dom.groupSizeSummary, true);
            } else {
                // Show the expanded version
                this.updateGroupSizeDisplay();
            }
        },

        /**
         * Hide the group size section
         */
        hideGroupSizeSection: function() {
            const dom = MassageBooking.DOM.elements;
            MassageBooking.DOM.toggleVisibility(dom.groupSizeSection, false);
        },

        /**
         * Collapse the group size section and show summary
         */
        collapseGroupSizeSection: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;

            // Hide group size counter and next button
            MassageBooking.DOM.toggleVisibility(document.getElementById('group-size-counter'), false);
            MassageBooking.DOM.toggleVisibility(dom.groupSizeNextButton, false);

            // Hide the group size header
            MassageBooking.DOM.toggleVisibility(document.getElementById('group-size-header'), false);

            // Show the group size summary
            MassageBooking.DOM.toggleVisibility(dom.groupSizeSummary, true);
            document.getElementById('group-size-summary-text').textContent = `${state.groupSize} People`;

            // Update state
            state.groupSizeCollapsed = true;
        },

        /**
         * Expand the group size section to show counter
         */
        expandGroupSizeSection: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;

            // Show group size counter and hide summary
            MassageBooking.DOM.toggleVisibility(document.getElementById('group-size-counter'), true);
            MassageBooking.DOM.toggleVisibility(dom.groupSizeSummary, false);

            // Show the group size header
            MassageBooking.DOM.toggleVisibility(document.getElementById('group-size-header'), true);

            // Show next button
            MassageBooking.DOM.toggleVisibility(dom.groupSizeNextButton, true);

            // Update state
            state.groupSizeCollapsed = false;

            // Update display
            this.updateGroupSizeDisplay();
        },

        /**
         * Get maximum group size based on selected duration (10-hour work day limit)
         * @returns {number} Maximum number of people for the selected duration
         */
        getMaxGroupSize: function() {
            const state = MassageBooking.State;
            const maxWorkMinutes = 600; // 10 hours = 600 minutes

            if (!state.selectedDuration) {
                // If no duration selected yet, use the most restrictive (120 min)
                return Math.floor(maxWorkMinutes / 120); // 5 people
            }

            return Math.floor(maxWorkMinutes / state.selectedDuration.time);
        },

        /**
         * Increase group size
         */
        increaseGroupSize: function() {
            const state = MassageBooking.State;
            const maxGroupSize = this.getMaxGroupSize();

            if (state.groupSize < maxGroupSize) {
                state.groupSize++;
                this.updateGroupSizeDisplay();
                this.updateDurationPrices();
            }
        },

        /**
         * Decrease group size
         */
        decreaseGroupSize: function() {
            const state = MassageBooking.State;
            const minGroupSize = 3; // Corporate minimum

            if (state.groupSize > minGroupSize) {
                state.groupSize--;
                this.updateGroupSizeDisplay();
                this.updateDurationPrices();
            }
        },

        /**
         * Update the group size display and button states (legacy function for old interface)
         */
        updateGroupSizeDisplay: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;

            // Only update if the old group size elements exist
            if (dom.peopleCountDisplay) {
                dom.peopleCountDisplay.textContent = state.groupSize;
            }

            // Update button states only if elements exist
            const minGroupSize = 3;
            const maxGroupSize = this.getMaxGroupSize();

            if (dom.decreasePeopleButton) {
                dom.decreasePeopleButton.disabled = state.groupSize <= minGroupSize;
            }

            if (dom.increasePeopleButton) {
                dom.increasePeopleButton.disabled = state.groupSize >= maxGroupSize;
            }

            // Update the info text to show current limits only if element exists
            const counterInfo = document.querySelector('.counter-info p');
            if (counterInfo) {
                let infoText = `Minimum 3 people for corporate sessions`;
                if (state.selectedDuration) {
                    infoText += ` • Maximum ${maxGroupSize} people for ${state.selectedDuration.time}-minute sessions (10-hour work day limit)`;
                } else {
                    infoText += ` • Maximum will be set based on duration selected`;
                }
                counterInfo.textContent = infoText;
            }

            // Show next button when group size is set only if element exists
            if (dom.groupSizeNextButton && state.groupSize >= minGroupSize) {
                MassageBooking.DOM.toggleVisibility(dom.groupSizeNextButton, true);
            }
        },

        /**
         * Update duration prices when group size changes
         */
        updateDurationPrices: function() {
            const state = MassageBooking.State;

            // Only update if we have a selected service and person
            if (state.selectedService && state.selectedPerson && state.selectedPerson.id === 'corporate') {
                // Re-populate durations to update prices
                this.populateDurations();
            }
        },

        /**
         * Show notification when group size is automatically adjusted due to duration limits
         * @param {number} newGroupSize - The new group size
         * @param {number} durationMinutes - The selected duration in minutes
         */
        showGroupSizeAdjustmentNotification: function(newGroupSize, durationMinutes) {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = 'group-size-notification';
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(to right, hsl(184, 70%, 35%), hsl(184, 70%, 45%));
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 0.5rem;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                z-index: 10000;
                font-family: 'Lato', sans-serif;
                font-size: 0.9rem;
                max-width: 300px;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
            `;

            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-info-circle" style="font-size: 1.1rem;"></i>
                    <div>
                        <strong>Group size adjusted</strong><br>
                        Maximum ${newGroupSize} people for ${durationMinutes}-minute sessions (10-hour work day limit)
                    </div>
                </div>
            `;

            // Add to page
            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Remove after 5 seconds
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 5000);
        },

        /**
         * Show the unified selection section (now corporate sessions)
         */
        showUnifiedSelection: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;

            MassageBooking.DOM.toggleVisibility(dom.unifiedSelectionSection, true);

            // If the section is already collapsed, keep it collapsed and don't interfere
            if (state.corporateSessionsCollapsed) {
                // Make sure the summary is shown and interface is hidden
                MassageBooking.DOM.toggleVisibility(dom.unifiedSelectionInterface, false);
                MassageBooking.DOM.toggleVisibility(dom.unifiedSelectionNextButton, false);
                MassageBooking.DOM.toggleVisibility(document.getElementById('unified-selection-header'), false);
                MassageBooking.DOM.toggleVisibility(dom.unifiedSelectionSummary, true);
                // Don't call populateCorporateSessions when collapsed to avoid interference
            } else {
                // Show the expanded version - make sure summary is hidden initially
                MassageBooking.DOM.toggleVisibility(dom.unifiedSelectionSummary, false);
                MassageBooking.DOM.toggleVisibility(document.getElementById('unified-selection-header'), true);
                MassageBooking.DOM.toggleVisibility(dom.unifiedSelectionInterface, true);
                this.populateCorporateSessions();
            }
        },

        /**
         * Hide the unified selection section
         */
        hideUnifiedSelection: function() {
            const dom = MassageBooking.DOM.elements;
            MassageBooking.DOM.toggleVisibility(dom.unifiedSelectionSection, false);
        },

        /**
         * Collapse the corporate sessions section and show summary
         */
        collapseCorporateSessionsSection: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;
            const calculator = MassageBooking.Calculator;

            // Hide the interface and next button
            MassageBooking.DOM.toggleVisibility(dom.unifiedSelectionInterface, false);
            MassageBooking.DOM.toggleVisibility(dom.unifiedSelectionNextButton, false);

            // Hide the header
            MassageBooking.DOM.toggleVisibility(document.getElementById('unified-selection-header'), false);

            // Show the summary
            MassageBooking.DOM.toggleVisibility(dom.unifiedSelectionSummary, true);
            const summaryText = `${state.corporateSessions} sessions (${calculator.formatCorporateTime()}) • $${calculator.calculateCorporatePrice()}`;
            document.getElementById('unified-selection-summary-text').textContent = summaryText;

            // Update state
            state.corporateSessionsCollapsed = true;

            // Update UI to trigger booking section visibility check
            this.updateUI();
        },

        /**
         * Expand the corporate sessions section
         */
        expandCorporateSessionsSection: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;

            // Show the interface and header
            MassageBooking.DOM.toggleVisibility(dom.unifiedSelectionInterface, true);
            MassageBooking.DOM.toggleVisibility(document.getElementById('unified-selection-header'), true);

            // Hide the summary
            MassageBooking.DOM.toggleVisibility(dom.unifiedSelectionSummary, false);

            // Update state
            state.corporateSessionsCollapsed = false;

            // Refresh the interface
            this.populateCorporateSessions();
        },

        /**
         * Collapse the unified selection section and show summary (LEGACY)
         */
        collapseUnifiedSelection: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;

            if (state.selectedUnifiedOption) {
                // Hide the interface and header
                MassageBooking.DOM.toggleVisibility(dom.unifiedSelectionInterface, false);
                MassageBooking.DOM.toggleVisibility(document.getElementById('unified-selection-header'), false);

                // Show the summary
                MassageBooking.DOM.toggleVisibility(dom.unifiedSelectionSummary, true);
                const summaryText = `${state.selectedUnifiedOption.duration.time} minutes • ${state.selectedUnifiedOption.groupSize} people • $${state.selectedUnifiedOption.totalPrice}`;
                document.getElementById('unified-selection-summary-text').textContent = summaryText;

                // Update state
                state.unifiedSelectionCollapsed = true;
            }
        },

        /**
         * Expand the unified selection section
         */
        expandUnifiedSelection: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;

            // Show the interface and header
            MassageBooking.DOM.toggleVisibility(dom.unifiedSelectionInterface, true);
            MassageBooking.DOM.toggleVisibility(document.getElementById('unified-selection-header'), true);

            // Hide the summary
            MassageBooking.DOM.toggleVisibility(dom.unifiedSelectionSummary, false);

            // Update state
            state.unifiedSelectionCollapsed = false;

            // Refresh the interface
            this.populateUnifiedSelection();
        },

        /**
         * Show the service section
         */
        showServiceSection: function() {
            const dom = MassageBooking.DOM.elements;
            // Make sure the service section is visible
            dom.serviceSection.classList.remove('hidden-section');
            dom.serviceSection.style.display = 'block';
            dom.serviceSection.classList.remove('hidden');
        },

        /**
         * Hide the service section
         */
        hideServiceSection: function() {
            const dom = MassageBooking.DOM.elements;
            MassageBooking.DOM.toggleVisibility(dom.serviceSection, false);
        },

        /**
         * Collapse the service section and show summary
         */
        collapseServiceSection: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;

            if (state.selectedService) {
                // Hide service options and next button
                MassageBooking.DOM.toggleVisibility(dom.serviceOptionsContainer, false);
                MassageBooking.DOM.toggleVisibility(dom.serviceNextButton, false);

                // Hide the service header with the "Choose Your Massage Style" text
                MassageBooking.DOM.toggleVisibility(document.getElementById('service-header'), false);

                // Show the service summary in place of the header
                MassageBooking.DOM.toggleVisibility(dom.serviceSummary, true);
                document.getElementById('service-summary-text').textContent = state.selectedService.name;

                // Update state
                state.serviceCollapsed = true;
            }
        },

        /**
         * Expand the service section to show all options
         */
        expandServiceSection: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;

            // Show service options and hide summary
            MassageBooking.DOM.toggleVisibility(dom.serviceOptionsContainer, true);
            MassageBooking.DOM.toggleVisibility(dom.serviceSummary, false);

            // Show the service header with the "Choose Your Massage Style" text
            MassageBooking.DOM.toggleVisibility(document.getElementById('service-header'), true);

            // Show next button if a service is selected
            if (state.selectedService) {
                MassageBooking.DOM.toggleVisibility(dom.serviceNextButton, true);
            }

            // Update state
            state.serviceCollapsed = false;

            // Reset animation for service options
            document.querySelectorAll('.service-option').forEach((option, index) => {
                // Reset the animation by removing and re-adding the element to the DOM
                option.style.animation = 'none';
                option.offsetHeight; // Trigger reflow
                option.style.animation = '';
                option.style.setProperty('--animation-order', index);
            });
        },

        /**
         * Show the duration section
         */
        showDurationSection: function() {
            const dom = MassageBooking.DOM.elements;
            MassageBooking.DOM.toggleVisibility(dom.durationSection, true);
        },

        /**
         * Hide the duration section
         */
        hideDurationSection: function() {
            const dom = MassageBooking.DOM.elements;
            MassageBooking.DOM.toggleVisibility(dom.durationSection, false);
        },

        /**
         * Collapse the duration section and show summary
         */
        collapseDurationSection: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;
            const calculator = MassageBooking.Calculator;

            if (state.selectedDuration) {
                // Hide duration options
                MassageBooking.DOM.toggleVisibility(dom.durationOptionsContainer, false);

                // Show duration summary
                MassageBooking.DOM.toggleVisibility(dom.durationSummary, true);
                dom.durationSummary.textContent = `${state.selectedDuration.time} minutes - $${calculator.calculatePriceForDuration(state.selectedDuration)}`;

                // Show edit button
                MassageBooking.DOM.toggleVisibility(dom.editDurationButton, true);

                // Update state
                state.durationCollapsed = true;
            }
        },

        /**
         * Expand the duration section to show all options
         */
        expandDurationSection: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;

            // Show duration options
            MassageBooking.DOM.toggleVisibility(dom.durationOptionsContainer, true);

            // Hide duration summary and edit button
            MassageBooking.DOM.toggleVisibility(dom.durationSummary, false);
            MassageBooking.DOM.toggleVisibility(dom.editDurationButton, false);

            // Update state
            state.durationCollapsed = false;
        },

        /**
         * Populate person options in the UI
         */
        populatePersonOptions: function() {
            const dom = MassageBooking.DOM.elements;
            const persons = MassageBooking.Data.persons;

            // Clear existing options
            dom.personOptionsContainer.innerHTML = '';

            // Create person option elements
            persons.forEach((person, index) => {
                const personElement = document.createElement('div');
                personElement.className = 'service-option';

                // Set animation order for staggered appearance
                personElement.style.setProperty('--animation-order', index);

                // Set inner HTML with person details and accessibility attributes
                personElement.setAttribute('role', 'radio');
                personElement.setAttribute('aria-checked', 'false');
                personElement.setAttribute('tabindex', '0');
                personElement.setAttribute('aria-label', `${person.name}: ${person.description}`);
                personElement.innerHTML = `
                    <div class="flex items-center gap-3">
                        <div class="service-selector-icon" aria-hidden="true">
                            <i class="${person.icon}"></i>
                        </div>
                        <div class="service-text-container">
                            <h3 class="service-selector-h3 font-semibold">${person.name}</h3>
                            <p class="service-selector-p text-sm">${person.description}</p>
                        </div>
                    </div>
                `;

                // Add click and keyboard event listeners for accessibility
                const selectPerson = () => {
                    // Update selected state visually
                    document.querySelectorAll('#person-options .service-option').forEach(el => {
                        MassageBooking.DOM.toggleClass(el, 'selected', false);
                        el.setAttribute('aria-checked', 'false');
                        el.tabIndex = 0;
                    });
                    MassageBooking.DOM.toggleClass(personElement, 'selected', true);
                    personElement.setAttribute('aria-checked', 'true');
                    personElement.tabIndex = -1; // Remove from tab order once selected

                    // Update application state
                    MassageBooking.State.selectedPerson = person;
                    MassageBooking.State.selectedService = null;
                    MassageBooking.State.selectedDuration = null;

                    // Reset group size state
                    MassageBooking.State.groupSizeCollapsed = false;
                    if (person.id === 'corporate') {
                        MassageBooking.State.groupSize = 3;
                    }

                    // Show the Next button
                    MassageBooking.DOM.toggleVisibility(dom.personNextButton, true);

                    // Repopulate service options based on the new person selection
                    this.populateServices();

                    // Update UI
                    this.updateUI();
                };

                // Mouse click event
                personElement.addEventListener('click', selectPerson);

                // Keyboard event for accessibility
                personElement.addEventListener('keydown', (e) => {
                    // Select on Space or Enter key
                    if (e.key === ' ' || e.key === 'Enter') {
                        e.preventDefault();
                        selectPerson();
                    }
                });

                // Add to container
                dom.personOptionsContainer.appendChild(personElement);
            });
        },

        /**
         * Populate service options in the UI
         */
        populateServices: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;
            const allServices = MassageBooking.Data.services;

            // Filter services based on person selection
            let services = allServices;
            if (state.selectedPerson && state.selectedPerson.id === 'individual') {
                // Remove Chair massage for individual sessions
                services = allServices.filter(service => service.id !== 'chair');
            } else if (state.selectedPerson && state.selectedPerson.id === 'couples') {
                // Remove Chair massage for couples
                services = allServices.filter(service => service.id !== 'chair');
            } else if (state.selectedPerson && state.selectedPerson.id === 'corporate') {
                // Show only Chair massage for corporate
                services = allServices.filter(service => service.id === 'chair');
            }

            // Clear existing options
            dom.serviceOptionsContainer.innerHTML = '';

            // Create service option elements
            services.forEach((service, index) => {
                const serviceElement = document.createElement('div');
                serviceElement.className = 'service-option';

                // Set animation order for staggered appearance
                serviceElement.style.setProperty('--animation-order', index);

                // Set inner HTML with service details and accessibility attributes
                serviceElement.setAttribute('role', 'radio');
                serviceElement.setAttribute('aria-checked', 'false');
                serviceElement.setAttribute('tabindex', '0');
                serviceElement.setAttribute('aria-label', `${service.name}: ${service.description}`);
                serviceElement.innerHTML = `
                    <div class="flex items-center gap-3">
                        <div class="service-selector-icon" aria-hidden="true">
                            <i class="${service.icon}"></i>
                        </div>
                        <div class="service-text-container">
                            <h3 class="service-selector-h3 font-semibold">${service.name}</h3>
                            <p class="service-selector-p text-sm">${service.description}</p>
                        </div>
                    </div>
                `;

                // Add click and keyboard event listeners for accessibility
                const selectService = () => {
                    // Update selected state visually
                    document.querySelectorAll('#service-options .service-option').forEach(el => {
                        MassageBooking.DOM.toggleClass(el, 'selected', false);
                        el.setAttribute('aria-checked', 'false');
                        el.tabIndex = 0;
                    });
                    MassageBooking.DOM.toggleClass(serviceElement, 'selected', true);
                    serviceElement.setAttribute('aria-checked', 'true');
                    serviceElement.tabIndex = -1; // Remove from tab order once selected

                    // Update application state
                    MassageBooking.State.selectedService = service;
                    MassageBooking.State.selectedDuration = null;

                    // Reset group size state when changing service
                    MassageBooking.State.groupSizeCollapsed = false;

                    // Show the Next button
                    MassageBooking.DOM.toggleVisibility(dom.serviceNextButton, true);

                    // Update durations and UI
                    this.populateDurations();
                    MassageBooking.Calculator.recalculateMaxRelaxationScore();
                    this.updateUI();
                };

                // Mouse click event
                serviceElement.addEventListener('click', selectService);

                // Keyboard event for accessibility
                serviceElement.addEventListener('keydown', (e) => {
                    // Select on Space or Enter key
                    if (e.key === ' ' || e.key === 'Enter') {
                        e.preventDefault();
                        selectService();
                    }
                });

                // Add to container
                dom.serviceOptionsContainer.appendChild(serviceElement);
            });
        },

        /**
         * Populate duration options based on selected service and person type
         */
        populateDurations: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;
            const calculator = MassageBooking.Calculator;

            // Clear existing options
            dom.durationOptionsContainer.innerHTML = '';

            // Exit if no service or person is selected
            if (!state.selectedService || !state.selectedPerson) return;

            // Get appropriate durations for the selected service and person type
            const durations = MassageBooking.Data.getDurationsForService(state.selectedService.id, state.selectedPerson.id);

            // Create duration option elements
            durations.forEach(duration => {
                const durationElement = document.createElement('div');
                durationElement.className = 'duration-option text-center';

                // Set inner HTML with duration details and accessibility attributes
                durationElement.setAttribute('role', 'radio');
                durationElement.setAttribute('aria-checked', 'false');
                durationElement.setAttribute('tabindex', '0');
                durationElement.setAttribute('aria-label', `${duration.time} minutes for $${calculator.calculatePriceForDuration(duration)}`);
                durationElement.innerHTML = `
                    <div class="font-semibold duration-selector-h3" style="font-family: 'Poppins', sans-serif;">${duration.time} min</div>
                    <div class="text-sm duration-selector-p" style="font-family: 'Poppins', sans-serif; font-size: 0.85rem; margin-top: 0.1rem; line-height: 1.2rem; ">$${calculator.calculatePriceForDuration(duration)}</div>
                `;

                // Add click and keyboard event listeners for accessibility
                const selectDuration = () => {
                    // Update selected state visually
                    document.querySelectorAll('.duration-option').forEach(el => {
                        MassageBooking.DOM.toggleClass(el, 'selected', false);
                        el.setAttribute('aria-checked', 'false');
                        el.tabIndex = 0;
                    });
                    MassageBooking.DOM.toggleClass(durationElement, 'selected', true);
                    durationElement.setAttribute('aria-checked', 'true');
                    durationElement.tabIndex = -1; // Remove from tab order once selected

                    // Update application state
                    state.selectedDuration = duration;

                    // For corporate sessions, check if current group size exceeds new duration limit
                    if (state.selectedPerson && state.selectedPerson.id === 'corporate') {
                        const maxGroupSize = this.getMaxGroupSize();
                        if (state.groupSize > maxGroupSize) {
                            state.groupSize = maxGroupSize;

                            // Show a brief notification about the adjustment
                            this.showGroupSizeAdjustmentNotification(maxGroupSize, duration.time);
                        }
                        // Always update group size display to show new limits
                        this.updateGroupSizeDisplay();
                    }

                    // Update UI
                    this.updateUI();

                    // For individual/couples, scroll to booking section after duration selection
                    if (state.selectedPerson && state.selectedPerson.id !== 'corporate') {
                        setTimeout(() => {
                            this.scrollToBookingSection();
                        }, 300);
                    }
                };

                // Mouse click event
                durationElement.addEventListener('click', selectDuration);

                // Keyboard event for accessibility
                durationElement.addEventListener('keydown', (e) => {
                    // Select on Space or Enter key
                    if (e.key === ' ' || e.key === 'Enter') {
                        e.preventDefault();
                        selectDuration();
                    }
                });

                // Add to container
                dom.durationOptionsContainer.appendChild(durationElement);
            });
        },

        /**
         * Update UI based on current selections
         */
        updateUI: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;
            const calculator = MassageBooking.Calculator;

            // Calculate relaxation score for Rive animation
            const relaxationScore = calculator.calculateRelaxationScore();
            const relaxationPercentage = Math.min(100, Math.round((relaxationScore / state.maxRelaxationScore) * 100));

            // Update Rive animation
            updateRelaxationAnimation(relaxationPercentage);

            // Update Person Next button visibility
            if (dom.personNextButton) {
                MassageBooking.DOM.toggleVisibility(
                    dom.personNextButton,
                    state.selectedPerson && !state.personCollapsed
                );
            }

            // Update Service Next button visibility
            if (dom.serviceNextButton) {
                MassageBooking.DOM.toggleVisibility(
                    dom.serviceNextButton,
                    state.selectedService && !state.serviceCollapsed
                );
            }

            // Update Group Size Next button visibility (only for corporate after service selection)
            MassageBooking.DOM.toggleVisibility(
                dom.groupSizeNextButton,
                state.selectedPerson && state.selectedPerson.id === 'corporate' &&
                state.selectedService && state.serviceCollapsed && !state.groupSizeCollapsed
            );

            // Show appropriate sections based on selection state
            if (state.selectedPerson && state.personCollapsed) {
                this.showServiceSection();
            } else if (!state.selectedPerson) {
                this.hideServiceSection();
            }

            // Show unified selection for corporate after service is selected
            if (state.selectedPerson && state.selectedPerson.id === 'corporate' &&
                state.selectedService && state.serviceCollapsed) {
                this.showUnifiedSelection();

                // Update Next button visibility for corporate sessions (only if not collapsed)
                if (dom.unifiedSelectionNextButton && !state.corporateSessionsCollapsed) {
                    MassageBooking.DOM.toggleVisibility(
                        dom.unifiedSelectionNextButton,
                        state.corporateSessions >= MassageBooking.Data.corporate.minSessions
                    );
                }
            } else {
                this.hideUnifiedSelection();
            }

            // Hide old group size section for corporate (using unified selection instead)
            if (state.selectedPerson && state.selectedPerson.id === 'corporate') {
                this.hideGroupSizeSection();
            }

            // Show duration section based on flow
            if (state.selectedService && state.serviceCollapsed) {
                // For corporate: duration is handled by unified selection, don't show separate duration section
                if (state.selectedPerson && state.selectedPerson.id === 'corporate') {
                    this.hideDurationSection();
                } else {
                    // For individual/couples: show duration directly after service
                    this.showDurationSection();
                }
            } else if (!state.selectedService) {
                this.hideDurationSection();
            }

            // Update Book Now button visibility and animation
            this.updateBookNowSection();
        },

        /**
         * Update the Book Now section based on current selections
         */
        updateBookNowSection: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;
            const calculator = MassageBooking.Calculator;

            // Check if all required selections are made
            const isReadyForBooking = state.selectedPerson && state.selectedService &&
                (state.selectedDuration ||
                 (state.selectedPerson.id === 'corporate' && state.corporateSessions >= MassageBooking.Data.corporate.minSessions && state.corporateSessionsCollapsed));

            if (isReadyForBooking) {
                // Show the Book Now button when all selections are made
                MassageBooking.DOM.toggleVisibility(dom.bookNowContainer, true);

                // Calculate total price
                let totalPrice, durationTime;

                if (state.selectedPerson.id === 'corporate') {
                    totalPrice = calculator.calculateCorporatePrice();
                    durationTime = calculator.calculateCorporateTime();
                } else {
                    totalPrice = calculator.calculateTotalPrice();
                    durationTime = state.selectedDuration.time;
                }

                // Update the message with selected person, service, duration, and price
                const readyMessage = dom.bookNowContainer.querySelector('.mb-3.text-xl');

                // Add a subtle background and padding to the message
                readyMessage.style.padding = "1rem";
                readyMessage.style.borderRadius = "0.75rem";
                readyMessage.style.background = "rgba(255, 255, 255, 0.6)";
                readyMessage.style.boxShadow = "0 2px 8px rgba(79, 209, 197, 0.15)";
                readyMessage.style.marginBottom = "1.5rem";

                // Use stacked format with separators for both mobile and desktop
                let personInfo = state.selectedPerson.name;
                let durationInfo;

                if (state.selectedPerson.id === 'corporate') {
                    personInfo = 'Corporate';
                    durationInfo = `${state.corporateSessions} sessions (${calculator.formatCorporateTime()})`;
                } else {
                    durationInfo = `${durationTime} minutes`;
                }

                readyMessage.innerHTML = `
                <span class="ready-message-title" style="display: block; margin-bottom: 0.75rem; font-size: 1.25rem;">Your massage experience is ready!</span>
                <div class="ready-message-details">
                    <div class="ready-message-item" style="font-size: 1.3rem;">${personInfo}</div>
                    <div class="ready-message-item">${state.selectedService.name}</div>
                    <div class="ready-message-item">${durationInfo}</div>
                    <div class="ready-message-item">$${totalPrice}</div>
                </div>`;

                // Only play the full animation sequence the first time
                if (!state.summaryAnimationPlayed) {
                    this.animateSummarySection();
                    state.summaryAnimationPlayed = true;
                } else {
                    // For subsequent updates, just show the content without animation
                    this.showSummaryWithoutAnimation();
                }

                dom.bookButton.disabled = false;
            } else {
                // Hide the Book Now button if either selection is missing
                dom.bookNowContainer.classList.remove('visible');

                // Reset all animation classes and flag
                this.resetSummaryAnimation();
                state.summaryAnimationPlayed = false;

                // Use setTimeout to hide the element after the fade-out animation
                setTimeout(() => {
                    // Only hide if we don't meet the booking requirements
                    const shouldHide = !state.selectedService ||
                        (!state.selectedDuration &&
                         !(state.selectedPerson && state.selectedPerson.id === 'corporate' &&
                           state.corporateSessions >= MassageBooking.Data.corporate.minSessions &&
                           state.corporateSessionsCollapsed));

                    if (shouldHide) {
                        MassageBooking.DOM.toggleVisibility(dom.bookNowContainer, false);
                    }
                }, 350); // Match the transition duration

                dom.bookButton.disabled = true;
            }
        },

        /**
         * Animate the summary section with sequential reveal and progressive scrolling
         */
        animateSummarySection: function() {
            const dom = MassageBooking.DOM.elements;

            // First, show the container and make the title visible
            dom.bookNowContainer.classList.add('visible');

            // Step 1: Show the booking ready title container and start scrolling
            setTimeout(() => {
                const bookingTitle = document.getElementById('booking-ready-title');
                bookingTitle.classList.add('title-visible');

                // Start scrolling to bring the summary section into view
                this.scrollToElementSmoothly(dom.bookNowContainer, 'start');

                // Step 2: Animate in the "Your massage experience is ready!" text
                setTimeout(() => {
                    const readyMessageTitle = bookingTitle.querySelector('.ready-message-title');
                    if (readyMessageTitle) {
                        readyMessageTitle.classList.add('animate-in');
                    }

                    // Step 3: Show the details container
                    setTimeout(() => {
                        const detailsContainer = bookingTitle.querySelector('.ready-message-details');
                        if (detailsContainer) {
                            detailsContainer.classList.add('details-visible');

                            // Step 4: Animate in each detail item one by one with progressive scrolling
                            const detailItems = detailsContainer.querySelectorAll('.ready-message-item');
                            detailItems.forEach((item, index) => {
                                setTimeout(() => {
                                    item.classList.add('item-visible');

                                    // Progressive scroll adjustment as items appear
                                    if (index === Math.floor(detailItems.length / 2)) {
                                        // Scroll to center when we're halfway through the details
                                        this.scrollToElementSmoothly(dom.bookNowContainer, 'center');
                                    }
                                }, index * 200); // 200ms delay between each item
                            });

                            // Step 5: Animate in the Book Now button after all details are shown
                            setTimeout(() => {
                                const bookButton = dom.bookButton;
                                if (bookButton) {
                                    bookButton.classList.add('button-visible');

                                    // Final scroll to ensure the button is fully visible
                                    setTimeout(() => {
                                        this.scrollToElementSmoothly(dom.bookNowContainer, 'end');
                                    }, 200);
                                }
                            }, detailItems.length * 200 + 300); // Wait for all items + extra delay
                        }
                    }, 400); // Wait for title animation to complete
                }, 300); // Wait for container to expand
            }, 100); // Small initial delay
        },

        /**
         * Show summary content without animation (for subsequent updates)
         */
        showSummaryWithoutAnimation: function() {
            const dom = MassageBooking.DOM.elements;
            const bookingTitle = document.getElementById('booking-ready-title');

            // Show the container
            dom.bookNowContainer.classList.add('visible');

            // Immediately show all elements without animation
            if (bookingTitle) {
                bookingTitle.classList.add('title-visible');

                const readyMessageTitle = bookingTitle.querySelector('.ready-message-title');
                if (readyMessageTitle) {
                    readyMessageTitle.classList.add('animate-in');
                }

                const detailsContainer = bookingTitle.querySelector('.ready-message-details');
                if (detailsContainer) {
                    detailsContainer.classList.add('details-visible');

                    const detailItems = detailsContainer.querySelectorAll('.ready-message-item');
                    detailItems.forEach(item => {
                        item.classList.add('item-visible');
                    });
                }
            }

            // Show the book button
            if (dom.bookButton) {
                dom.bookButton.classList.add('button-visible');
            }

            // Simple scroll to ensure visibility without the progressive scrolling
            setTimeout(() => {
                this.scrollToElementSmoothly(dom.bookNowContainer, 'center', 100);
            }, 100);
        },

        /**
         * Reset all summary animation classes
         */
        resetSummaryAnimation: function() {
            const bookingTitle = document.getElementById('booking-ready-title');
            const dom = MassageBooking.DOM.elements;

            if (bookingTitle) {
                // Reset title container
                bookingTitle.classList.remove('title-visible');

                // Reset title text
                const readyMessageTitle = bookingTitle.querySelector('.ready-message-title');
                if (readyMessageTitle) {
                    readyMessageTitle.classList.remove('animate-in');
                }

                // Reset details container
                const detailsContainer = bookingTitle.querySelector('.ready-message-details');
                if (detailsContainer) {
                    detailsContainer.classList.remove('details-visible');

                    // Reset all detail items
                    const detailItems = detailsContainer.querySelectorAll('.ready-message-item');
                    detailItems.forEach(item => {
                        item.classList.remove('item-visible');
                    });
                }
            }

            // Reset book button
            if (dom.bookButton) {
                dom.bookButton.classList.remove('button-visible');
            }
        },

        /**
         * Scroll to an element with more control over positioning
         * @param {HTMLElement} targetElement - The element to scroll to
         * @param {string} position - Where to position the element ('start', 'center', 'end')
         * @param {number} delay - Delay before scrolling (default: 0ms)
         */
        scrollToElementSmoothly: function(targetElement, position = 'center', delay = 0) {
            if (targetElement) {
                setTimeout(() => {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: position,
                        inline: 'nearest'
                    });
                }, delay);
            }
        },

        /**
         * Scroll to a specific section smoothly
         * @param {HTMLElement} targetElement - The element to scroll to
         * @param {number} delay - Delay before scrolling (default: 100ms)
         */
        scrollToSection: function(targetElement, delay = 100) {
            if (targetElement) {
                setTimeout(() => {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start',
                        inline: 'nearest'
                    });
                }, delay);
            }
        },

        /**
         * Scroll to the booking section smoothly
         */
        scrollToBookingSection: function() {
            const dom = MassageBooking.DOM.elements;

            if (dom.bookNowContainer) {
                // Use the new scrolling method for consistency
                this.scrollToElementSmoothly(dom.bookNowContainer, 'center', 100);
            }
        },

        /**
         * Get the appropriate Setmore URL based on selections
         * @returns {string} The Setmore URL for the selected service
         */
        getSetmoreUrl: function() {
            const state = MassageBooking.State;

            // Setmore URL mapping based on person type, service, and duration
            const setmoreUrls = {
                individual: {
                    swedish: {
                        60: 'https://mobile-massage.setmore.com/services/bd223969-30ba-4b8b-9fe0-5450fbca5a96',
                        90: 'https://mobile-massage.setmore.com/services/53673f33-db9c-44d1-8bdf-a51707c7cb1d',
                        120: 'https://mobile-massage.setmore.com/services/e5e75139-009d-4552-9d40-cf5c092d7cd0'
                    },
                    deepTissue: {
                        60: 'https://mobile-massage.setmore.com/services/8566a925-95e7-447f-8e0e-a12572701a67',
                        90: 'https://mobile-massage.setmore.com/services/30705ce2-ca70-4066-8d04-ad6472d3718a',
                        120: 'https://mobile-massage.setmore.com/services/8de87e2b-4837-48b1-b6bc-f7708d064bd8'
                    },
                    sports: {
                        60: 'https://mobile-massage.setmore.com/services/18966351-5349-469a-ae5f-e3f03c4fc511',
                        90: 'https://mobile-massage.setmore.com/services/5374edea-f16d-46ca-a44c-e10960596bed',
                        120: 'https://mobile-massage.setmore.com/services/f24785be-2f3e-4170-bb5f-32ad5381b6ef'
                    },
                    neuromuscular: {
                        60: 'https://mobile-massage.setmore.com/services/7d812ebc-a31c-41e6-8e13-74d012adbef3',
                        90: 'https://mobile-massage.setmore.com/services/bfcaefbc-6afb-4fda-bf28-2f4e7faddd4d',
                        120: 'https://mobile-massage.setmore.com/services/6a28e727-be46-44a8-8c0d-5fd58f8ccd99'
                    }
                },
                couples: {
                    swedish: {
                        60: 'https://mobile-massage.setmore.com/services/3553970a-9bfa-4722-a23e-a4244a5f401a',
                        90: 'https://mobile-massage.setmore.com/services/7be76dc4-26f1-4062-843a-fe7096a7990d'
                    },
                    deepTissue: {
                        60: 'https://mobile-massage.setmore.com/services/3e675eb2-bfba-4937-b827-1923f18c58ea',
                        90: 'https://mobile-massage.setmore.com/services/888aa64e-1d27-45a7-bca7-18a8dc15d714'
                    },
                    sports: {
                        60: 'https://mobile-massage.setmore.com/services/b621cdb6-f1d2-45ba-b4a5-536d3663b04b',
                        90: 'https://mobile-massage.setmore.com/services/294c9616-6b80-47b3-9c20-3ca7fe34c045'
                    },
                    neuromuscular: {
                        60: 'https://mobile-massage.setmore.com/services/a00592aa-c0ae-48ae-b182-e82e779fc36a',
                        90: 'https://mobile-massage.setmore.com/services/405cdc6b-772a-450d-8710-c45f16023b9a'
                    }
                },
                corporate: {
                    // Corporate uses chair massage with hour-based sessions
                    chair: {
                        1: 'https://mobile-massage.setmore.com/services/84c60c43-9ce9-4050-8d55-290f76723ff9',
                        2: 'https://mobile-massage.setmore.com/services/eeeb489f-18bd-4734-a1b0-58035509b310',
                        3: 'https://mobile-massage.setmore.com/services/95e6608b-94a8-4695-b26d-c9ca2581bb81',
                        4: 'https://mobile-massage.setmore.com/services/7408e5a4-2879-4165-ad88-37bf8d19a7c8',
                        5: 'https://mobile-massage.setmore.com/services/89e1542c-9dbf-4fab-889e-8b13651650e7',
                        6: 'https://mobile-massage.setmore.com/services/9f6f53d8-d537-4bc4-8018-db06a3145e43',
                        7: 'https://mobile-massage.setmore.com/services/04e00818-ab4f-44ef-9dde-f39e7a18d533',
                        8: 'https://mobile-massage.setmore.com/services/a6edb669-00c5-4673-84ce-d156c33f0d4f',
                        9: 'https://mobile-massage.setmore.com/services/a7be2522-cbe1-414a-aa78-f3b7b7e396d6',
                        10: 'https://mobile-massage.setmore.com/services/31c9326b-a825-4d1e-86d0-fbb8fbbd77f4'
                    }
                }
            };

            // Determine the correct URL based on selections
            if (state.selectedPerson.id === 'corporate') {
                // Corporate sessions: calculate hours from sessions (4 sessions = 1 hour)
                const hours = Math.ceil(state.corporateSessions / 4);
                return setmoreUrls.corporate.chair[hours] || setmoreUrls.corporate.chair[1];
            } else {
                // Individual or couples: use service type and duration
                const personType = state.selectedPerson.id;
                const serviceType = state.selectedService.id;
                const duration = state.selectedDuration.time;

                return setmoreUrls[personType]?.[serviceType]?.[duration] || 'https://mobile-massage.setmore.com';
            }
        },

        /**
         * Handle the booking button click
         */
        handleBooking: function() {
            const state = MassageBooking.State;

            // Check if we have all required selections
            const hasRequiredSelections = state.selectedService &&
                (state.selectedDuration ||
                 (state.selectedPerson.id === 'corporate' && state.corporateSessions >= MassageBooking.Data.corporate.minSessions && state.corporateSessionsCollapsed));

            if (!hasRequiredSelections) return;

            // Get the appropriate Setmore URL and redirect directly
            const setmoreUrl = this.getSetmoreUrl();
            window.location.href = setmoreUrl;
        },

        /**
         * Show booking confirmation dialog
         * @param {Object} service - The selected service
         * @param {Object} duration - The selected duration
         * @param {number} totalPrice - The total price
         */
        showBookingConfirmation: function(service, duration, totalPrice) {
            // Create overlay with accessibility attributes
            const alertOverlay = document.createElement('div');
            alertOverlay.setAttribute('role', 'dialog');
            alertOverlay.setAttribute('aria-modal', 'true');
            alertOverlay.setAttribute('aria-labelledby', 'booking-confirmation-title');
            alertOverlay.setAttribute('aria-describedby', 'booking-confirmation-content');
            alertOverlay.style.position = 'fixed';
            alertOverlay.style.top = '0';
            alertOverlay.style.left = '0';
            alertOverlay.style.width = '100%';
            alertOverlay.style.height = '100%';
            alertOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            alertOverlay.style.display = 'flex';
            alertOverlay.style.justifyContent = 'center';
            alertOverlay.style.alignItems = 'center';
            alertOverlay.style.zIndex = '9999';

            // Create alert box
            const alertBox = document.createElement('div');
            alertBox.style.backgroundColor = 'white';
            alertBox.style.padding = '2rem';
            alertBox.style.borderRadius = '1rem';
            alertBox.style.maxWidth = '90%';
            alertBox.style.width = '400px';
            alertBox.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.2)';
            alertBox.style.textAlign = 'center';
            alertBox.style.position = 'relative';
            alertBox.style.border = '1px solid rgba(79, 209, 197, 0.3)';

            // Create title with ID for aria-labelledby
            const title = document.createElement('h2');
            title.id = 'booking-confirmation-title';
            title.textContent = 'Booking Confirmed!';
            title.style.color = 'hsl(184, 70%, 35%)';
            title.style.marginBottom = '1rem';
            title.style.fontFamily = "'Poppins', sans-serif";

            // Create content with ID for aria-describedby
            const content = document.createElement('div');
            content.id = 'booking-confirmation-content';
            content.style.marginBottom = '1.5rem';
            content.style.fontFamily = "'Lato', sans-serif";
            content.style.color = '#555';
            content.style.lineHeight = '1.6';
            const state = MassageBooking.State;
            let personInfo = state.selectedPerson.name;
            let durationInfo;

            if (state.selectedPerson.id === 'corporate') {
                personInfo = 'Corporate';
                durationInfo = `${duration.sessions} sessions (${MassageBooking.Calculator.formatCorporateTime()})`;
            } else {
                durationInfo = `${duration.time} minutes`;
            }

            content.innerHTML = `
                <p><strong>Session Type:</strong> ${personInfo}</p>
                <p><strong>Service:</strong> ${service.name}</p>
                <p><strong>Duration:</strong> ${durationInfo}</p>
                <p><strong>Total Price:</strong> $${totalPrice}</p>
            `;

            // Create thank you message
            const thankYou = document.createElement('p');
            thankYou.textContent = 'Thank you for your booking!';
            thankYou.style.fontWeight = 'bold';
            thankYou.style.marginBottom = '1.5rem';
            thankYou.style.color = '#333';

            // Create close button with accessibility attributes
            const closeButton = document.createElement('button');
            closeButton.textContent = 'Close';
            closeButton.setAttribute('aria-label', 'Close booking confirmation');
            closeButton.style.padding = '0.5rem 1.5rem';
            closeButton.style.background = 'linear-gradient(to right, hsl(184, 70%, 35%), hsl(184, 70%, 45%))';
            closeButton.style.color = 'white';
            closeButton.style.border = 'none';
            closeButton.style.borderRadius = '0.5rem';
            closeButton.style.cursor = 'pointer';
            closeButton.style.fontFamily = "'Lato', sans-serif";
            closeButton.style.fontWeight = 'bold';
            closeButton.style.textTransform = 'uppercase';
            closeButton.style.letterSpacing = '1px';
            closeButton.style.minHeight = '44px'; // Ensure adequate touch target

            // Add close button event listeners for accessibility
            closeButton.addEventListener('click', () => {
                document.body.removeChild(alertOverlay);
            });

            // Close on Escape key press
            const handleEscapeKey = (e) => {
                if (e.key === 'Escape') {
                    document.body.removeChild(alertOverlay);
                    document.removeEventListener('keydown', handleEscapeKey);
                }
            };
            document.addEventListener('keydown', handleEscapeKey);

            // Trap focus within the modal for accessibility
            alertBox.addEventListener('keydown', (e) => {
                if (e.key === 'Tab') {
                    // There's only one focusable element (the close button)
                    // so just prevent default and keep focus on the button
                    e.preventDefault();
                    closeButton.focus();
                }
            });

            // Assemble the alert
            alertBox.appendChild(title);
            alertBox.appendChild(content);
            alertBox.appendChild(thankYou);
            alertBox.appendChild(closeButton);
            alertOverlay.appendChild(alertBox);
            document.body.appendChild(alertOverlay);

            // Set focus to the close button when the dialog opens
            setTimeout(() => closeButton.focus(), 50);
        }
    }
};

/**
 * Initialize the entire application
 */
MassageBooking.init = function() {
    // Cache DOM elements
    this.DOM.cacheElements();

    // Initialize UI
    this.UI.init();
};

// Initialize the application when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize the application
    MassageBooking.init();
});
